import { ChartType } from '@/constants/chartTypes';
import {
  ReportData,
  ProcessData,
  AdvanceData,
  PostsData,
  CloudData,
  SourceInfoData,
  TopSourcesData,
  ContentTypeDistributionData,
  SentimentsData,
  CategoriesData,
  StatisticalData,
  EmotionData,
  TrendsData,
  InfluenceGraphData,
  ImpactGraphData,
  OpinionGraphData,
} from '@/types/dashboard';
import { formatLargeNumber } from './formatters';

// Chart data interfaces
export interface ChartSeries {
  name: string;
  data: number[] | Array<[number, number]>;
  type?: string;
}

export interface ChartData {
  series?: ChartSeries[];
  categories?: string[];
  data?: any[];
  nodes?: any[];
  links?: any[];
  value?: number;
  title?: string;
  maxHeight?: string;
}

// Data transformer interface
export interface DataTransformer {
  transform(data: any, chartType: ChartType, widget: any): ChartData;
}

// Base transformer class
abstract class BaseTransformer implements DataTransformer {
  abstract transform(data: any, chartType: ChartType, widget: any): ChartData;

  protected formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('fa-IR', {
      month: 'short',
      day: 'numeric',
    });
  }

  protected formatTimestamp(timestamp: number): string {
    const date = new Date(timestamp);
    return date.toLocaleDateString('fa-IR', {
      month: 'short',
      day: 'numeric',
    });
  }

  // Helper method to get selected platforms from widget
  protected getSelectedPlatforms(widget: any): string[] {
    return widget?.params?.query?.platform || ['twitter'];
  }

  // Helper method to get platform display name
  protected getPlatformDisplayName(platform: string): string {
    const platformNames: Record<string, string> = {
      twitter: 'توییتر',
      telegram: 'تلگرام',
      instagram: 'اینستاگرام',
      news: 'اخبار',
    };
    return platformNames[platform] || platform;
  }
}

// Process data transformer
class ProcessTransformer extends BaseTransformer {
  transform(
    data: { [platform: string]: ProcessData[] },
    chartType: ChartType,
    widget: any
  ): ChartData {
    const selectedPlatforms = this.getSelectedPlatforms(widget);

    // Handle single platform
    if (selectedPlatforms.length === 1) {
      const platform = selectedPlatforms[0];
      const platformData = data[platform] || [];

      const processedData = platformData.map((item) => ({
        x: new Date(item.timestamp)
          .toLocaleDateString('fa-IR', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
          })
          .replace(/[\u200E]/g, ''),
        y: item.count,
        name: this.formatDate(item.datetime),
      }));

      switch (chartType) {
        case 'line':
          return {
            data: [
              {
                name: 'تعداد',
                data: processedData.map((item) => [item.x, item.y]),
              },
            ],
            categories: processedData.map((item) => item.name),
          };
        case 'bar':
          return {
            data: [
              {
                name: 'تعداد',
                data: processedData.map((item) => item.y),
              },
            ],
            categories: processedData.map((item) => item.name),
          };
        case 'table':
          return {
            data: {
              headers: ['زمان', 'تعداد'],
              rows: processedData.map((item) => [item.x, item.y]),
            },
            maxHeight: widget?.params?.position?.height
              ? 50 * widget?.params?.position?.height + 'px'
              : '400px',
          };
        case 'list':
          return {
            data: platformData,
            maxHeight: widget?.params?.position?.height
              ? 50 * widget?.params?.position?.height + 'px'
              : '400px',
          };
        default:
          return { data: processedData };
      }
    } else {
      // Multiple platforms data - for stack charts
      const series: ChartSeries[] = selectedPlatforms.map((platform) => ({
        name: this.getPlatformDisplayName(platform),
        data: (data[platform] || []).map((item) => item.count),
      }));

      // Use the first platform's data for categories
      const firstPlatform = selectedPlatforms[0];
      const categories = (data[firstPlatform] || []).map((item) =>
        this.formatDate(item.datetime)
      );

      return { data: series, categories };
    }
  }
}

// Cloud data transformer
class CloudTransformer extends BaseTransformer {
  transform(
    data: { [platform: string]: SentimentsData[] },
    chartType: ChartType,
    widget: any
  ): ChartData {
    const selectedPlatforms = this.getSelectedPlatforms(widget);

    const platform = selectedPlatforms[0];
    const platformData = data[platform] || [];

    switch (chartType) {
      case 'cloud':
        return {
          data: platformData.map((item) => ({
            name: item.key,
            weight: item.count,
          })),
        };
      case 'bar':
        return {
          series: [
            {
              name: 'وزن',
              data: platformData.map((item) => item.count),
            },
          ],
          categories: platformData.map((item) => item.key),
        };
      case 'table':
        return {
          data: {
            headers: ['منبع', 'تعداد'],
            rows: platformData.map((item) => [
              item.source || item.title || item.key,
              item.count,
            ]),
          },
          maxHeight: widget?.params?.position?.height
            ? 50 * widget?.params?.position?.height + 'px'
            : '400px',
        };
      default:
        return { platformData };
    }
  }
}

// Source info transformer
class SourceInfoTransformer extends BaseTransformer {
  transform(
    data: { [platform: string]: SourceInfoData[] },
    chartType: ChartType,
    widget: any
  ): ChartData {
    const selectedPlatforms = this.getSelectedPlatforms(widget);

    // Handle single platform
    if (selectedPlatforms.length === 1) {
      const platform = selectedPlatforms[0];
      const platformData = data[platform] || [];

      switch (chartType) {
        case 'radial':
          return {
            data: [
              {
                name: 'تعداد',
                data: platformData.map((item) => item.count).slice(5),
              },
            ],
            categories: platformData
              .map((item) => item.source || item.title)
              .slice(5),
          };
        case 'table':
          return {
            data: {
              headers: ['منبع', 'تعداد'],
              rows: platformData.map((item) => [
                item.source || item.title,
                item.count,
              ]),
            },
            maxHeight: widget?.params?.position?.height
              ? 50 * widget?.params?.position?.height + 'px'
              : '400px',
          };
        default:
          return { data: platformData };
      }
    } else {
      // Multiple platforms data - for stack charts
      const allSources = new Set<string>();
      selectedPlatforms.forEach((platform) => {
        (data[platform] || []).forEach((item) => {
          allSources.add(item.source || item.title);
        });
      });

      const sources = Array.from(allSources);
      const series: ChartSeries[] = selectedPlatforms.map((platform) => ({
        name: this.getPlatformDisplayName(platform),
        data: sources.slice(5).map((source) => {
          const item = (data[platform] || []).find(
            (d) => (d.source || d.title) === source
          );
          return item ? item.count : 0;
        }),
      }));

      switch (chartType) {
        case 'radial':
          return {
            data: series,
            categories: sources.slice(5),
          };
        case 'table':
          return {
            data: {
              headers: ['منبع', 'تعداد'],
              rows: platformData.map((item) => [
                item.source || item.title,
                item.count,
              ]),
            },
            maxHeight: widget?.params?.position?.height
              ? 50 * widget?.params?.position?.height + 'px'
              : '400px',
          };
        default:
          return { data: series, categories: sources };
      }
    }
  }
}

// Sentiments transformer
class SentimentsTransformer extends BaseTransformer {
  transform(
    data: { [platform: string]: SentimentsData[] },
    chartType: ChartType,
    widget: any
  ): ChartData {
    const selectedPlatforms = this.getSelectedPlatforms(widget);
    const sentimentLabels = {
      positive: 'مثبت',
      negative: 'منفی',
      neutral: 'خنثی',
    };

    // Handle single platform
    if (selectedPlatforms.length === 1) {
      const platform = selectedPlatforms[0];
      const platformData = data[platform] || [];

      switch (chartType) {
        case 'pie':
        case 'donut':
        case 'semi_pie':
          return {
            data: platformData.map((item) => ({
              name: sentimentLabels[item.key || item.sentiment],
              y: item.count,
            })),
          };
        case 'radar':
        case 'spider':
          return {
            data: [
              {
                name: 'احساسات',
                data: platformData.map((item) => item.count),
                pointPlacement: 'on',
              },
            ],
            categories: platformData.map(
              (item) => sentimentLabels[item.key || item.sentiment]
            ),
          };
        case 'bar':
          return {
            data: [
              {
                name: 'تعداد',
                data: platformData.map((item) => item.count),
              },
            ],
            categories: platformData.map(
              (item) => sentimentLabels[item.key || item.sentiment]
            ),
          };
        default:
          return { data: platformData };
      }
    } else {
      // Multiple platforms data - for stack charts
      const allSentiments = new Set<string>();
      selectedPlatforms.forEach((platform) => {
        (data[platform] || []).forEach((item) => {
          allSentiments.add(item.key || item.sentiment);
        });
      });

      const sentiments = Array.from(allSentiments);
      const series: ChartSeries[] = selectedPlatforms.map((platform) => ({
        name: this.getPlatformDisplayName(platform),
        data: sentiments.map((sentiment) => {
          const item = (data[platform] || []).find(
            (d) => (d.key || d.sentiment) === sentiment
          );
          return item ? item.count : 0;
        }),
      }));

      const categories = sentiments.map(
        (sentiment) => sentimentLabels[sentiment] || sentiment
      );
      return { data: series, categories };
    }
  }
}

// Categories transformer
class CategoriesTransformer extends BaseTransformer {
  transform(data: EmotionData[], chartType: ChartType, widget: any): ChartData {
    const selectedPlatforms = this.getSelectedPlatforms(widget);
    const sentimentLabels = {
      politic: 'سیاسی',
      culture_art: 'فرهنگی هنری',
      security_defense: 'امنیتی دفاعی',
      dailylife: 'روزمره',
      economic: 'اقتصادی',
      social: 'اجتماعی',
      science_tech_health: 'علمی، فناوری و سلامت',
      religion: 'مذهبی',
      sport: 'ورزشی',
    };

    // Handle single platform
    if (selectedPlatforms.length === 1) {
      const platform = selectedPlatforms[0];
      const platformData = data[platform] || [];

      switch (chartType) {
        case 'pie':
        case 'donut':
        case 'semi_pie':
          return {
            data: platformData.map((item) => ({
              name: sentimentLabels[item.key || item.sentiment],
              y: item.count,
            })),
          };
        case 'radar':
        case 'spider':
          return {
            data: [
              {
                name: 'تعداد',
                data: platformData.map((item) => item.count),
                pointPlacement: 'on',
              },
            ],
            categories: platformData.map(
              (item) => sentimentLabels[item.key || item.sentiment]
            ),
          };
        case 'bar':
          return {
            data: [
              {
                name: 'تعداد',
                data: platformData.map((item) => item.count),
              },
            ],
            categories: platformData.map(
              (item) => sentimentLabels[item.key || item.sentiment]
            ),
          };
        default:
          return { data: platformData };
      }
    } else {
      // Multiple platforms data - for stack charts
      const allSentiments = new Set<string>();
      selectedPlatforms.forEach((platform) => {
        (data[platform] || []).forEach((item) => {
          allSentiments.add(item.key || item.sentiment);
        });
      });

      const sentiments = Array.from(allSentiments);
      const series: ChartSeries[] = selectedPlatforms.map((platform) => ({
        name: this.getPlatformDisplayName(platform),
        data: sentiments.map((sentiment) => {
          const item = (data[platform] || []).find(
            (d) => (d.key || d.sentiment) === sentiment
          );
          return item ? item.count : 0;
        }),
      }));

      const categories = sentiments.map(
        (sentiment) => sentimentLabels[sentiment] || sentiment
      );
      return { data: series, categories };
    }
  }
}

// Statistical transformer
class StatisticalTransformer extends BaseTransformer {
  transform(
    data: StatisticalData[],
    chartType: ChartType,
    widget: any
  ): ChartData {
    const selectedPlatforms = this.getSelectedPlatforms(widget);

    const LABELS = [
      { value: 'view', label: 'تعداد بازدید', unit: 'نفر' },
      { value: 'like', label: 'تعداد لایک', unit: 'نفر' },
      { value: 'comment', label: 'تعداد کامنت', unit: 'نفر' },
      { value: 'retweet', label: 'تعداد بازنشر', unit: 'نفر' },
      { value: 'repost', label: 'تعداد بازنشر', unit: 'نفر' },
      { value: 'post', label: 'تعداد پست', unit: 'عدد' },
      { value: 'source', label: 'تعداد منابع', unit: 'عدد' },
    ];

    const getLabel = (value) => {
      return LABELS.find((x) => x.value === value)?.label || 'یافت شده';
    };

    const getUnit = (value) => {
      return LABELS.find((x) => x.value === value)?.unit || '';
    };

    // Handle single platform
    if (selectedPlatforms.length === 1) {
      const platform = selectedPlatforms[0];
      const platformData = data[platform] || [];

      const subject =
        widget.report_type.indexOf('%') > 0
          ? widget.report_type?.split('%')?.[1] || ''
          : '';

      switch (chartType) {
        case 'badge': {
          const rawValue =
            platformData.filter((item) => item.key === subject)?.[0]?.count ||
            0;
          return {
            data: {
              value: formatLargeNumber(rawValue),
              label: getLabel(subject),
              unit: getUnit(subject),
            },
          };
        }

        default:
          return { data: platformData };
      }
    } else {
      // Multiple platforms data - for stack charts
      const series: ChartSeries[] = selectedPlatforms.map((platform) => ({
        name: this.getPlatformDisplayName(platform),
        data: (data[platform] || []).map((item) => item.count),
      }));

      switch (chartType) {
        case 'badge':
          return { value: data[0]?.value || 0 };
        case 'bar':
          return {
            series: [
              {
                name: 'مقدار',
                data: data.map((item) => item.value),
              },
            ],
            categories: data.map((item) => item.metric),
          };
        case 'table':
          return {
            data: data.map((item) => ({
              متریک: item.metric,
              مقدار: item.value,
              تغییر: item.change || 0,
              نوع_تغییر: item.changeType || 'stable',
            })),
          };
        default:
          return { data };
      }

      // Use the first platform's data for categories
      const firstPlatform = selectedPlatforms[0];
      const categories = (data[firstPlatform] || []).map((item) =>
        this.formatDate(item.datetime)
      );

      return { data: series, categories };
    }
  }
}

// Posts transformer
class PostsTransformer extends BaseTransformer {
  transform(
    data: { [platform: string]: SentimentsData[] },
    chartType: ChartType,
    widget: any
  ): ChartData {
    const selectedPlatforms = this.getSelectedPlatforms(widget);

    const platform = selectedPlatforms[0];
    const platformData = data[platform] || [];

    switch (chartType) {
      case 'list':
        return {
          data: platformData,
        };

      default:
        return { platformData };
    }
  }
}

// Emotion transformer
class EmotionTransformer extends BaseTransformer {
  transform(data: EmotionData[], chartType: ChartType, widget: any): ChartData {
    const selectedPlatforms = this.getSelectedPlatforms(widget);
    const sentimentLabels = {
      anger: 'خشم',
      disgust: 'نفرت',
      sadness: 'اندوه',
      fear: 'ترس',
      neutral: 'خنثی',
      disappointment: 'ناامیدی',
      optimism: 'خوش‌بینی',
      joy: 'شادی',
      trust: 'اعتماد',
      surprise: 'هیجان',
    };

    // Handle single platform
    if (selectedPlatforms.length === 1) {
      const platform = selectedPlatforms[0];
      const platformData = data[platform] || [];

      switch (chartType) {
        case 'pie':
        case 'donut':
        case 'semi_pie':
          return {
            data: platformData.map((item) => ({
              name: sentimentLabels[item.key || item.sentiment],
              y: item.count,
            })),
          };
        case 'radar':
        case 'spider':
          return {
            data: [
              {
                name: 'تعداد',
                data: platformData.map((item) => item.count),
                pointPlacement: 'on',
              },
            ],
            categories: platformData.map(
              (item) => sentimentLabels[item.key || item.sentiment]
            ),
          };
        case 'bar':
          return {
            data: [
              {
                name: 'تعداد',
                data: platformData.map((item) => item.count),
              },
            ],
            categories: platformData.map(
              (item) => sentimentLabels[item.key || item.sentiment]
            ),
          };
        default:
          return { data: platformData };
      }
    } else {
      // Multiple platforms data - for stack charts
      const allSentiments = new Set<string>();
      selectedPlatforms.forEach((platform) => {
        (data[platform] || []).forEach((item) => {
          allSentiments.add(item.key || item.sentiment);
        });
      });

      const sentiments = Array.from(allSentiments);
      const series: ChartSeries[] = selectedPlatforms.map((platform) => ({
        name: this.getPlatformDisplayName(platform),
        data: sentiments.map((sentiment) => {
          const item = (data[platform] || []).find(
            (d) => (d.key || d.sentiment) === sentiment
          );
          return item ? item.count : 0;
        }),
      }));

      const categories = sentiments.map(
        (sentiment) => sentimentLabels[sentiment] || sentiment
      );
      return { data: series, categories };
    }
  }
}

// Trends transformer
class TrendsTransformer extends BaseTransformer {
  transform(data: TrendsData[], chartType: ChartType): ChartData {
    switch (chartType) {
      case 'line':
        return {
          series: [
            {
              name: 'روند',
              data: data.map((item) => [
                new Date(item.datetime).getTime(),
                item.value,
              ]),
            },
          ],
          categories: data.map((item) => this.formatDate(item.datetime)),
        };
      case 'bar':
        return {
          series: [
            {
              name: 'مقدار',
              data: data.map((item) => item.value),
            },
          ],
          categories: data.map((item) => this.formatDate(item.datetime)),
        };
      default:
        return { data };
    }
  }
}

// Graph transformers
class InfluenceGraphTransformer extends BaseTransformer {
  transform(data: InfluenceGraphData, chartType: ChartType): ChartData {
    switch (chartType) {
      case 'network_graph':
        return {
          nodes: data.nodes,
          links: data.links,
        };
      default:
        return { data };
    }
  }
}

class ImpactGraphTransformer extends BaseTransformer {
  transform(data: ImpactGraphData, chartType: ChartType): ChartData {
    switch (chartType) {
      case 'network_graph':
        return {
          nodes: data.nodes,
          links: data.links,
        };
      default:
        return { data };
    }
  }
}

class OpinionGraphTransformer extends BaseTransformer {
  transform(data: OpinionGraphData, chartType: ChartType): ChartData {
    switch (chartType) {
      case 'network_graph':
        return {
          nodes: data.nodes,
          links: data.links,
        };
      default:
        return { data };
    }
  }
}

// Content type distribution transformer
class ContentTypeDistributionTransformer extends BaseTransformer {
  transform(
    data: ContentTypeDistributionData[],
    chartType: ChartType
  ): ChartData {
    switch (chartType) {
      case 'pie':
      case 'donut':
        return {
          series: [
            {
              name: 'نوع محتوا',
              data: data.map((item) => ({
                name: item.type,
                y: item.count,
              })),
            },
          ],
        };
      case 'bar':
        return {
          series: [
            {
              name: 'تعداد',
              data: data.map((item) => item.count),
            },
          ],
          categories: data.map((item) => item.type),
        };
      default:
        return { data };
    }
  }
}

// Search suggest transformer
class SearchSuggestTransformer extends BaseTransformer {
  transform(data: any[], chartType: ChartType): ChartData {
    switch (chartType) {
      case 'cloud':
        return {
          data: data.map((item) => ({
            name: item.query || item.phrase || item.text,
            weight: item.relevance || item.count || item.weight,
          })),
        };
      case 'bar':
        return {
          series: [
            {
              name: 'امتیاز',
              data: data.map(
                (item) => item.relevance || item.count || item.weight
              ),
            },
          ],
          categories: data.map(
            (item) => item.query || item.phrase || item.text
          ),
        };
      case 'list':
        return {
          data: data.map((item) => ({
            title: item.query || item.phrase || item.text,
            score: item.relevance || item.count || item.weight,
            description: item.description || '',
          })),
        };
      default:
        return { data };
    }
  }
}

// Offensive content transformer
class OffensiveTransformer extends BaseTransformer {
  transform(data: EmotionData[], chartType: ChartType, widget: any): ChartData {
    const selectedPlatforms = this.getSelectedPlatforms(widget);
    const sentimentLabels = {
      'non-offensive': 'غیر توهین آمیز',
      offensive: 'توهین آمیز',
    };

    // Handle single platform
    if (selectedPlatforms.length === 1) {
      const platform = selectedPlatforms[0];
      const platformData = data[platform] || [];

      switch (chartType) {
        case 'pie':
        case 'donut':
        case 'semi_pie':
          return {
            data: platformData.map((item) => ({
              name: sentimentLabels[item.key || item.sentiment],
              y: item.count,
            })),
          };
        case 'radar':
        case 'spider':
          return {
            data: [
              {
                name: 'تعداد',
                data: platformData.map((item) => item.count),
                pointPlacement: 'on',
              },
            ],
            categories: platformData.map(
              (item) => sentimentLabels[item.key || item.sentiment]
            ),
          };
        case 'bar':
          return {
            data: [
              {
                name: 'تعداد',
                data: platformData.map((item) => item.count),
              },
            ],
            categories: platformData.map(
              (item) => sentimentLabels[item.key || item.sentiment]
            ),
          };
        default:
          return { data: platformData };
      }
    } else {
      // Multiple platforms data - for stack charts
      const allSentiments = new Set<string>();
      selectedPlatforms.forEach((platform) => {
        (data[platform] || []).forEach((item) => {
          allSentiments.add(item.key || item.sentiment);
        });
      });

      const sentiments = Array.from(allSentiments);
      const series: ChartSeries[] = selectedPlatforms.map((platform) => ({
        name: this.getPlatformDisplayName(platform),
        data: sentiments.map((sentiment) => {
          const item = (data[platform] || []).find(
            (d) => (d.key || d.sentiment) === sentiment
          );
          return item ? item.count : 0;
        }),
      }));

      const categories = sentiments.map(
        (sentiment) => sentimentLabels[sentiment] || sentiment
      );
      return { data: series, categories };
    }
  }
}

// Advertise content transformer
class AdvertiseTransformer extends BaseTransformer {
  transform(data: EmotionData[], chartType: ChartType, widget: any): ChartData {
    const selectedPlatforms = this.getSelectedPlatforms(widget);
    const sentimentLabels = {
      'non-adv': 'غیر تبلیغاتی',
      adv: 'تبلیغاتی',
    };

    // Handle single platform
    if (selectedPlatforms.length === 1) {
      const platform = selectedPlatforms[0];
      const platformData = data[platform] || [];

      switch (chartType) {
        case 'pie':
        case 'donut':
        case 'semi_pie':
          return {
            data: platformData.map((item) => ({
              name: sentimentLabels[item.key || item.sentiment],
              y: item.count,
            })),
          };
        case 'radar':
        case 'spider':
          return {
            data: [
              {
                name: 'تعداد',
                data: platformData.map((item) => item.count),
                pointPlacement: 'on',
              },
            ],
            categories: platformData.map(
              (item) => sentimentLabels[item.key || item.sentiment]
            ),
          };
        case 'bar':
          return {
            data: [
              {
                name: 'تعداد',
                data: platformData.map((item) => item.count),
              },
            ],
            categories: platformData.map(
              (item) => sentimentLabels[item.key || item.sentiment]
            ),
          };
        default:
          return { data: platformData };
      }
    } else {
      // Multiple platforms data - for stack charts
      const allSentiments = new Set<string>();
      selectedPlatforms.forEach((platform) => {
        (data[platform] || []).forEach((item) => {
          allSentiments.add(item.key || item.sentiment);
        });
      });

      const sentiments = Array.from(allSentiments);
      const series: ChartSeries[] = selectedPlatforms.map((platform) => ({
        name: this.getPlatformDisplayName(platform),
        data: sentiments.map((sentiment) => {
          const item = (data[platform] || []).find(
            (d) => (d.key || d.sentiment) === sentiment
          );
          return item ? item.count : 0;
        }),
      }));

      const categories = sentiments.map(
        (sentiment) => sentimentLabels[sentiment] || sentiment
      );
      return { data: series, categories };
    }
  }
}

// Related content transformer
class RelatedContentTransformer extends BaseTransformer {
  transform(data: any[], chartType: ChartType): ChartData {
    switch (chartType) {
      case 'list':
        return {
          data: data.map((item) => ({
            title: item.content?.substring(0, 100) + '...' || 'محتوا',
            similarity: `${Math.round((item.similarity || 0) * 100)}%`,
            date: item.datetime ? this.formatDate(item.datetime) : '',
            source: item.source || 'نامشخص',
          })),
        };
      case 'bar':
        return {
          series: [
            {
              name: 'شباهت',
              data: data.map((item) =>
                Math.round((item.similarity || 0) * 100)
              ),
            },
          ],
          categories: data.map((item, index) => `محتوا ${index + 1}`),
        };
      case 'table':
        return {
          data: data.map((item) => ({
            محتوا: item.content?.substring(0, 50) + '...' || 'محتوا',
            شباهت: `${Math.round((item.similarity || 0) * 100)}%`,
            تاریخ: item.datetime ? this.formatDate(item.datetime) : '',
            منبع: item.source || 'نامشخص',
          })),
        };
      default:
        return { data };
    }
  }
}

// Trends graph transformer (for multiple categories)
class TrendsGraphTransformer extends BaseTransformer {
  transform(data: any, chartType: ChartType): ChartData {
    if (typeof data === 'object' && !Array.isArray(data)) {
      // Multiple categories data
      const categories = Object.keys(data);
      const series: ChartSeries[] = categories.map((category) => ({
        name: category,
        data: data[category].map((item: any) => [
          new Date(item.datetime).getTime(),
          item.value,
        ]),
      }));

      const timeCategories =
        data[categories[0]]?.map((item: any) =>
          this.formatDate(item.datetime)
        ) || [];

      return { series, categories: timeCategories };
    }

    // Fallback to regular trends transformer
    return new TrendsTransformer().transform(data, chartType);
  }
}

// Transformer factory
export class DataTransformerFactory {
  private static transformers: Record<string, DataTransformer> = {
    process: new ProcessTransformer(),
    top_sources: new SourceInfoTransformer(),
    sentiments: new SentimentsTransformer(),
    statistical: new StatisticalTransformer(),
    advance: new ProcessTransformer(),
    posts: new PostsTransformer(),
    cloud: new CloudTransformer(),
    source_info: new SourceInfoTransformer(),
    content_type_distribution: new ContentTypeDistributionTransformer(),
    search_in_source: new PostsTransformer(),
    similar_phrases: new SearchSuggestTransformer(),
    source_suggest: new SearchSuggestTransformer(),
    categories: new CategoriesTransformer(),
    search_suggest: new SearchSuggestTransformer(),
    offensive: new OffensiveTransformer(),
    advertise: new AdvertiseTransformer(),
    influence_graph: new InfluenceGraphTransformer(),
    impact_graph: new ImpactGraphTransformer(),
    related_content: new RelatedContentTransformer(),
    emotion: new EmotionTransformer(),
    trends: new TrendsTransformer(),
    trends_graph: new TrendsGraphTransformer(),
    trends_statistical: new StatisticalTransformer(),
    trends_content_age: new CategoriesTransformer(),
    trends_account_credential: new CategoriesTransformer(),
    opinion_graph: new OpinionGraphTransformer(),
    opinion_emotion: new EmotionTransformer(),
    opinion_sentiments: new SentimentsTransformer(),
    opinion_age: new CategoriesTransformer(),
    opinion_gender: new CategoriesTransformer(),
    opinion_categories: new CategoriesTransformer(),
    opinion_top_sources: new SourceInfoTransformer(),
  };

  static getTransformer(reportType: string): DataTransformer | null {
    return this.transformers[reportType] || null;
  }

  static transform(
    data: any,
    widget: any,
    reportType: string,
    chartType: ChartType
  ): ChartData {
    const transformer = this.getTransformer(reportType);
    if (!transformer) {
      console.warn(`No transformer found for report type: ${reportType}`);
      return { data };
    }

    try {
      return transformer.transform(data, chartType, widget);
    } catch (error) {
      console.error(`Error transforming data for ${reportType}:`, error);
      return { data };
    }
  }
}
