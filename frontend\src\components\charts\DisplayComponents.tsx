import React from 'react';
import { CHART_COLORS } from './chartConfig';
import { TwitterLogoIcon } from '@phosphor-icons/react';
import { formatLargeNumber } from '@/utils/formatters';

// Types for display components
export interface TableData {
  headers: string[];
  rows: Array<Array<string | number>>;
}

export interface SingleValueData {
  value: string | number;
  label?: string;
  unit?: string;
  trend?: 'up' | 'down' | 'neutral';
  trendValue?: number;
}

export interface ListItem {
  id: string | number;
  title: string;
  description?: string;
  value?: string | number;
  date?: string;
  category?: string;
  url?: string;
}

export interface TwitterPostData {
  id: string;
  user_id: string;
  user_name: string;
  user_title: string;
  avatar: string;
  post_type: string;
  post_url: string;
  text: string;
  media?: {
    image?: string[];
    video?: string[];
  };
  quote?: any;
  like_count: number;
  retweet_count: number;
  comment_count: number;
  view_count: number;
  bookmark_count: number;
  date: string;
  influence?: any;
}

/**
 * جدول داده‌ها
 */
export const DataTable: React.FC<{
  data?: TableData;
  title?: string;
  className?: string;
  maxHeight?: string;
  striped?: boolean;
  bordered?: boolean;
}> = ({
  data = {
    headers: [],
    rows: [],
  },
  title = 'جدول داده‌ها',
  className = '',
  maxHeight = '400px',
  striped = true,
  bordered = true,
}) => {
  return (
    <div
      className={`h-full w-full overflow-hidden rounded-[5px] border border-[#323538] bg-[#181A1B] p-4 ${className}`}
    >
      {title && (
        <h3
          className="mb-4 text-lg font-semibold text-white"
          style={{ fontFamily: 'IranYekanX' }}
        >
          {title}
        </h3>
      )}
      <div className="no-scrollbar mb-2 overflow-auto" style={{ maxHeight }}>
        <table className="w-full text-sm">
          <thead>
            <tr className={`${bordered ? 'border-b border-[#323538]' : ''}`}>
              {data.headers.map((header, index) => (
                <th
                  key={index}
                  className="px-4 py-3 text-right font-semibold text-white"
                  style={{
                    fontFamily: 'IranYekanX',
                    backgroundColor: CHART_COLORS[0],
                  }}
                >
                  {header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {data.rows.map((row, rowIndex) => (
              <tr
                key={rowIndex}
                className={` ${striped && rowIndex % 2 === 1 ? 'bg-[#1F2937]' : ''} ${bordered ? 'border-b border-[#323538]' : ''} transition-colors hover:bg-[#374151]`}
              >
                {row.map((cell, cellIndex) => (
                  <td
                    key={cellIndex}
                    className="px-3 py-2 text-right text-gray-300"
                    style={{ fontFamily: 'IranYekanX' }}
                  >
                    {cell}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

/**
 * نمایش تک مقداری
 */
export const SingleValueDisplay: React.FC<{
  data?: SingleValueData;
  title?: string;
  className?: string;
  size?: 'small' | 'medium' | 'large';
  showTrend?: boolean;
}> = ({
  data = {
    value: 1250,
    label: 'کل بازدیدکنندگان',
    unit: 'نفر',
    trend: 'up',
    trendValue: 12.5,
  },
  title,
  className = '',
  size = 'medium',
  showTrend = false,
}) => {
  console.log('data: ');
  console.log(data);
  const sizeClasses = {
    small: 'text-2xl',
    medium: 'text-4xl',
    large: 'text-6xl',
  };

  const getTrendIcon = (trend?: string) => {
    switch (trend) {
      case 'up':
        return '↗';
      case 'down':
        return '↘';
      default:
        return '→';
    }
  };

  const getTrendColor = (trend?: string) => {
    switch (trend) {
      case 'up':
        return '#5BF7FA';
      case 'down':
        return '#ff6b6b';
      default:
        return '#9EFEFF';
    }
  };

  return (
    <div
      className={`h-full w-full rounded-[5px] border border-[#323538] bg-[#181A1B] p-6 ${className}`}
    >
      {/* {title && (
        <h3
          className="mb-4 text-lg font-semibold text-white"
          style={{ fontFamily: 'IranYekanX' }}
        >
          {title}
        </h3>
      )} */}
      <div className="flex h-full flex-col items-center justify-center text-center">
        <div
          className={`${sizeClasses[size]} mb-2 font-bold`}
          style={{
            color: CHART_COLORS[3],
            fontFamily: 'IranYekanX',
          }}
        >
          {data.value}
          {data.unit && (
            <span className="mr-2 text-lg text-gray-400">{data.unit}</span>
          )}
        </div>

        {data.label && (
          <div
            className="mb-3 text-lg text-gray-300"
            style={{ fontFamily: 'IranYekanX' }}
          >
            {data.label}
          </div>
        )}

        {showTrend && data.trend && data.trendValue && (
          <div
            className="flex items-center text-sm font-medium"
            style={{
              color: getTrendColor(data.trend),
              fontFamily: 'IranYekanX',
            }}
          >
            <span className="ml-1 text-lg">{getTrendIcon(data.trend)}</span>
            {data.trendValue}%
            <span className="mr-2 text-gray-400">نسبت به قبل</span>
          </div>
        )}
      </div>
    </div>
  );
};

/**
 * نمایش لیستی مطالب
 */
export const ContentList: React.FC<{
  data?: TwitterPostData[];
  title?: string;
  className?: string;
  maxHeight?: string;
}> = ({
  data = [
    {
      id: 'b5a0c1a8-9814-34c1-8ee9-21b290fb69b0',
      user_id: '940847601921986562',
      user_name: 'hafezeh_tarikhi',
      user_title: 'حافظه تاریخی',
      avatar: 'https://s3.synappse.ir/twitter/profiles/940847601921986562.jpg',
      post_type: 'post',
      post_url:
        'https://twitter.com/hafezeh_tarikhi/status/1950471908949942693',
      text: 'اعلام آمادگی علی ضیا، مجری، برای اعزام به سوریه جهت «دفاع از حرمین شریفین حضرت زینب و حضرت رقیه» در جریان جنبش تصویری لبیک یا زینب هیئت الرضا تهران (۴ آبان ۱۳۹۲)',
      media: {
        image: ['https://pbs.twimg.com/media/GxF3rIVWYAAmVU-.jpg'],
        video: [],
      },
      like_count: 24113,
      retweet_count: 1808,
      comment_count: 317,
      view_count: 454334,
      bookmark_count: 566,
      date: '2025-07-30T08:21:55+00:00',
    },
  ],
  title = 'پست‌های توییتر',
  className = '',
  maxHeight = '500px',
}) => {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60 * 60)
    );

    if (diffInHours < 1) {
      return 'اکنون';
    } else if (diffInHours < 24) {
      return `${diffInHours} ساعت پیش`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays} روز پیش`;
    }
  };
  return (
    <div
      className={`h-full w-full overflow-hidden rounded-[5px] border border-[#323538] bg-[#181A1B] p-4 ${className}`}
    >
      {title && (
        <h3
          className="mb-4 text-lg font-semibold text-white"
          style={{ fontFamily: 'IranYekanX' }}
        >
          {title}
        </h3>
      )}
      <div className="no-scrollbar mb-2 overflow-auto" style={{ maxHeight }}>
        <div className="space-y-4">
          {data.map((post) => (
            <div
              key={post.id}
              className="rounded-lg border border-[#374151] bg-[#1F2937] p-4 transition-colors hover:bg-[#374151]"
            >
              {/* Header with avatar, user info, and platform badge */}
              <div className="mb-3 flex items-start justify-between">
                <div className="flex items-start gap-3">
                  <img
                    src={post.avatar}
                    alt={post.user_title}
                    className="h-12 w-12 rounded-full object-cover"
                    onError={(e) => {
                      e.currentTarget.src =
                        'https://via.placeholder.com/48x48/374151/ffffff?text=?';
                    }}
                  />
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <h4
                        className="text-base font-bold text-white"
                        style={{ fontFamily: 'IranYekanX' }}
                      >
                        {post.user_title}
                      </h4>
                      <TwitterLogoIcon size={16} className="fill-[#1DA1F2]" />
                    </div>
                    <p
                      className="text-sm text-gray-400"
                      style={{ fontFamily: 'IranYekanX' }}
                    >
                      @{post.user_name}
                    </p>
                  </div>
                </div>
                <span
                  className="text-xs text-gray-500"
                  style={{ fontFamily: 'IranYekanX' }}
                >
                  {formatDate(post.date)}
                </span>
              </div>

              {/* Post content */}
              <div className="mb-3">
                <p
                  className="text-sm leading-relaxed text-gray-200"
                  style={{ fontFamily: 'IranYekanX' }}
                >
                  {post.text}
                </p>
              </div>

              {/* Media */}
              {post.media?.image && post.media.image.length > 0 && (
                <div className="mb-3">
                  <img
                    src={post.media.image[0]}
                    alt="Post media"
                    className="w-full rounded-lg object-cover"
                    style={{ maxHeight: '300px' }}
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                </div>
              )}

              {/* Engagement stats */}
              <div className="flex items-center justify-between border-t border-[#374151] pt-3">
                <div className="flex items-center gap-6 text-xs text-gray-400">
                  <div className="flex items-center gap-1">
                    <span>💬</span>
                    <span style={{ fontFamily: 'IranYekanX' }}>
                      {formatLargeNumber(post.comment_count)}
                    </span>
                  </div>
                  <div className="flex items-center gap-1">
                    <span>🔄</span>
                    <span style={{ fontFamily: 'IranYekanX' }}>
                      {formatLargeNumber(post.retweet_count)}
                    </span>
                  </div>
                  <div className="flex items-center gap-1">
                    <span>❤️</span>
                    <span style={{ fontFamily: 'IranYekanX' }}>
                      {formatLargeNumber(post.like_count)}
                    </span>
                  </div>
                  <div className="flex items-center gap-1">
                    <span>👁️</span>
                    <span style={{ fontFamily: 'IranYekanX' }}>
                      {formatLargeNumber(post.view_count)}
                    </span>
                  </div>
                  <div className="flex items-center gap-1">
                    <span>🔖</span>
                    <span style={{ fontFamily: 'IranYekanX' }}>
                      {formatLargeNumber(post.bookmark_count)}
                    </span>
                  </div>
                </div>
                {/* <a
                  href={post.post_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-xs text-[#5BF7FA] hover:underline"
                  style={{ fontFamily: 'IranYekanX' }}
                >
                  مشاهده در توییتر
                </a> */}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
