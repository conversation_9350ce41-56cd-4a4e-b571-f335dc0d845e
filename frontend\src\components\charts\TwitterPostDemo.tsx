import React from 'react';
import { ContentList, TwitterPostData } from './DisplayComponents';

// Sample Twitter post data matching your API response structure
const sampleTwitterPosts: TwitterPostData[] = [
  {
    id: "b5a0c1a8-9814-34c1-8ee9-21b290fb69b0",
    user_id: "940847601921986562",
    user_name: "hafe<PERSON>h_tarik<PERSON>",
    user_title: "حافظه تاریخی",
    avatar: "https://s3.synappse.ir/twitter/profiles/940847601921986562.jpg",
    post_type: "post",
    post_url: "https://twitter.com/hafezeh_tarikhi/status/1950471908949942693",
    text: "اعلام آمادگی علی ضیا، مجری، برای اعزام به سوریه جهت «دفاع از حرمین شریفین حضرت زینب و حضرت رقیه» در جریان جنبش تصویری لبیک یا زینب هیئت الرضا تهران (۴ آبان ۱۳۹۲) https://t.co/seOG3Q4Ta3",
    media: {
      image: ["https://pbs.twimg.com/media/GxF3rIVWYAAmVU-.jpg"],
      video: []
    },
    quote: null,
    like_count: 24113,
    retweet_count: 1808,
    comment_count: 317,
    view_count: 454334,
    bookmark_count: 566,
    date: "2025-07-30T08:21:55+00:00",
    influence: null,
  },
  {
    id: "c6b1d2b9-0925-45d2-9ff0-32c401gc70c1",
    user_id: "123456789012345678",
    user_name: "news_account",
    user_title: "خبرگزاری فارس",
    avatar: "https://via.placeholder.com/48x48/1DA1F2/ffffff?text=FA",
    post_type: "post",
    post_url: "https://twitter.com/news_account/status/1950471908949942694",
    text: "آخرین اخبار از تهران: بررسی وضعیت ترافیک و آب و هوا در پایتخت. شهروندان از استفاده از وسایل نقلیه عمومی استفاده کنند.",
    media: {
      image: [],
      video: []
    },
    quote: null,
    like_count: 1250,
    retweet_count: 89,
    comment_count: 45,
    view_count: 12500,
    bookmark_count: 67,
    date: "2025-08-08T14:30:00+00:00",
    influence: null,
  }
];

export const TwitterPostDemo: React.FC = () => {
  return (
    <div className="p-6 bg-[#0F1419] min-h-screen">
      <h1 className="text-2xl font-bold text-white mb-6" style={{ fontFamily: 'IranYekanX' }}>
        نمایش پست‌های توییتر
      </h1>
      
      <div className="max-w-2xl">
        <ContentList 
          data={sampleTwitterPosts}
          title="آخرین پست‌های توییتر"
          maxHeight="600px"
        />
      </div>
    </div>
  );
};

export default TwitterPostDemo;
